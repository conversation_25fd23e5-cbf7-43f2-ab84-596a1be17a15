import os
import base64
import json
from datetime import datetime, <PERSON><PERSON><PERSON>
from decimal import Decimal
from typing import List, Optional, Tu<PERSON>
from fastapi import UploadFile
import openai
from app.models import FoodEntry, MealLog, MealSession, User
from app.api.schemas.meal_logs import AISuggestion, FoodEntryResponse, SessionTotals


class AIClassificationService:
    FOOD_DATABASE = {
        "sambar": {"kcal": 133, "protein": 4.2, "carbs": 18.5, "fat": 4.8, "fibre": 3.2},
        "rasam": {"kcal": 45, "protein": 1.8, "carbs": 8.2, "fat": 1.2, "fibre": 1.5},
        "rice": {"kcal": 130, "protein": 2.7, "carbs": 28.0, "fat": 0.3, "fibre": 0.4},
        "dal": {"kcal": 116, "protein": 9.0, "carbs": 20.0, "fat": 0.4, "fibre": 8.0},
        "chapati": {"kcal": 297, "protein": 11.0, "carbs": 51.0, "fat": 7.0, "fibre": 11.0},
        "curry": {"kcal": 150, "protein": 5.0, "carbs": 12.0, "fat": 8.0, "fibre": 3.0},
        "chicken": {"kcal": 165, "protein": 31.0, "carbs": 0.0, "fat": 3.6, "fibre": 0.0},
        "fish": {"kcal": 206, "protein": 22.0, "carbs": 0.0, "fat": 12.0, "fibre": 0.0},
        "vegetables": {"kcal": 25, "protein": 1.0, "carbs": 5.0, "fat": 0.2, "fibre": 2.5},
        "bread": {"kcal": 265, "protein": 9.0, "carbs": 49.0, "fat": 3.2, "fibre": 2.7},
        "pasta": {"kcal": 131, "protein": 5.0, "carbs": 25.0, "fat": 1.1, "fibre": 1.8},
        "salad": {"kcal": 20, "protein": 1.5, "carbs": 4.0, "fat": 0.2, "fibre": 2.0},
    }

    @classmethod
    async def classify_food_with_openai(cls, image_path: str, total_weight_g: float) -> Tuple[List[AISuggestion], List[dict]]:
        try:
            client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

            with open(image_path, "rb") as image_file:
                image_data = base64.b64encode(image_file.read()).decode('utf-8')

            response = client.chat.completions.create(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": f"""Analyze this food image and identify the food items. The total weight is {total_weight_g}g.

                                Return a JSON response with:
                                1. "suggestions": Array of food items with confidence scores (0-1)
                                2. "food_entries": Array of detected foods with estimated weights

                                Format:
                                {{
                                    "suggestions": [
                                        {{"label": "Food Name", "confidence": 0.85}}
                                    ],
                                    "food_entries": [
                                        {{"label": "Food Name", "weight_g": 120.5}}
                                    ]
                                }}

                                Focus on common foods like rice, dal, sambar, curry, vegetables, chicken, fish, bread, pasta, salad, etc."""
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_data}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=500
            )

            ai_response = json.loads(response.choices[0].message.content)

            suggestions = [
                AISuggestion(label=item["label"], confidence=item["confidence"])
                for item in ai_response.get("suggestions", [])
            ]

            entries = []
            for item in ai_response.get("food_entries", []):
                food_key = item["label"].lower()
                nutritional_info = cls.FOOD_DATABASE.get(food_key, cls.FOOD_DATABASE["curry"])
                entries.append({
                    "label": item["label"],
                    "weight_g": item["weight_g"],
                    "nutritional_info": nutritional_info
                })

            return suggestions, entries

        except Exception as e:
            return cls._fallback_classification(total_weight_g)

    @classmethod
    def _fallback_classification(cls, total_weight_g: float) -> Tuple[List[AISuggestion], List[dict]]:
        suggestions = [
            AISuggestion(label="Mixed Food", confidence=0.5),
        ]

        if total_weight_g > 100:
            entries = [
                {
                    "label": "Rice",
                    "weight_g": total_weight_g * 0.4,
                    "nutritional_info": cls.FOOD_DATABASE["rice"]
                },
                {
                    "label": "Curry",
                    "weight_g": total_weight_g * 0.6,
                    "nutritional_info": cls.FOOD_DATABASE["curry"]
                }
            ]
        else:
            entries = [
                {
                    "label": "Food Item",
                    "weight_g": total_weight_g,
                    "nutritional_info": cls.FOOD_DATABASE["curry"]
                }
            ]

        return suggestions, entries

    @classmethod
    def calculate_nutrition(cls, weight_g: float, nutritional_info: dict) -> dict:
        factor = weight_g / 100.0
        return {
            "estimated_kcal": nutritional_info["kcal"] * factor,
            "protein_g": nutritional_info["protein"] * factor,
            "carbs_g": nutritional_info["carbs"] * factor,
            "fat_g": nutritional_info["fat"] * factor,
            "fibre_g": nutritional_info["fibre"] * factor,
        }


class MealSessionService:
    SESSION_TIMEOUT_MINUTES = 30
    
    @classmethod
    def get_or_create_session(cls, device_id: str, timestamp: datetime, user: Optional[User] = None) -> MealSession:
        if not user:
            user, _ = User.objects.get_or_create(
                username=f"device_{device_id}",
                defaults={"email": f"device_{device_id}@smartplate.ai"}
            )
        
        recent_session = MealSession.objects.filter(
            device_id=device_id,
            end_time__gte=timestamp - timedelta(minutes=cls.SESSION_TIMEOUT_MINUTES)
        ).first()
        
        if recent_session:
            recent_session.end_time = timestamp
            recent_session.save()
            return recent_session
        else:
            return MealSession.objects.create(
                user=user,
                device_id=device_id,
                start_time=timestamp,
                end_time=timestamp,
            )
    
    @classmethod
    def update_session_totals(cls, session: MealSession) -> SessionTotals:
        entries = FoodEntry.objects.filter(meal_log__meal_session=session)
        
        total_kcal = sum(entry.estimated_kcal for entry in entries)
        protein_g = sum(entry.protein_g for entry in entries)
        carbs_g = sum(entry.carbs_g for entry in entries)
        fat_g = sum(entry.fat_g for entry in entries)
        fibre_g = sum(entry.fibre_g for entry in entries)
        
        session.total_kcal = total_kcal
        session.protein_g = protein_g
        session.carbs_g = carbs_g
        session.fat_g = fat_g
        session.fibre_g = fibre_g
        session.save()
        
        return SessionTotals(
            total_kcal=float(total_kcal),
            protein_g=float(protein_g),
            carbs_g=float(carbs_g),
            fat_g=float(fat_g),
            fibre_g=float(fibre_g),
        )


class MealLogService:
    @classmethod
    async def create_meal_log(
        cls,
        timestamp: datetime,
        total_weight_g: float,
        delta_weight_g: float,
        device_id: str,
        image_file: UploadFile,
        user: Optional[User] = None
    ) -> dict:
        session = MealSessionService.get_or_create_session(device_id, timestamp, user)

        meal_log = MealLog.objects.create(
            meal_session=session,
            timestamp=timestamp,
            image=image_file,
            total_weight_g=Decimal(str(total_weight_g)),
            delta_weight_g=Decimal(str(delta_weight_g)),
            status="confirmed",
        )
        
        ai_suggestions, food_entries_data = await AIClassificationService.classify_food_with_openai(
            meal_log.image.path, total_weight_g
        )

        meal_log.ai_suggestions = [{"label": s.label, "confidence": s.confidence} for s in ai_suggestions]
        meal_log.save()
        
        food_entries = []
        for entry_data in food_entries_data:
            nutrition = AIClassificationService.calculate_nutrition(
                entry_data["weight_g"], 
                entry_data["nutritional_info"]
            )
            
            food_entry = FoodEntry.objects.create(
                meal_log=meal_log,
                label=entry_data["label"],
                weight_g=Decimal(str(entry_data["weight_g"])),
                estimated_kcal=Decimal(str(nutrition["estimated_kcal"])),
                protein_g=Decimal(str(nutrition["protein_g"])),
                carbs_g=Decimal(str(nutrition["carbs_g"])),
                fat_g=Decimal(str(nutrition["fat_g"])),
                fibre_g=Decimal(str(nutrition["fibre_g"])),
                source="ai_auto",
            )
            food_entries.append(food_entry)
        
        session_totals = MealSessionService.update_session_totals(session)
        
        return {
            "meal_log_id": f"log_{meal_log.id}",
            "meal_session_id": f"sess_{session.id}",
            "status": meal_log.status,
            "image_url": meal_log.image.url if meal_log.image else "",
            "ai_suggestions": ai_suggestions,
            "entries": [
                FoodEntryResponse(
                    id=f"entry_{entry.id}",
                    label=entry.label,
                    weight_g=float(entry.weight_g),
                    estimated_kcal=float(entry.estimated_kcal),
                    protein_g=float(entry.protein_g),
                    carbs_g=float(entry.carbs_g),
                    fat_g=float(entry.fat_g),
                    fibre_g=float(entry.fibre_g),
                    manual_override=entry.manual_override,
                    source=entry.source,
                )
                for entry in food_entries
            ],
            "session_totals": session_totals,
        }
