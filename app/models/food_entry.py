from django.db import models
from django.utils.translation import gettext_lazy as _

from app.models.base import BaseModel
from app.models.meal_log import MealLog


class FoodEntrySource(models.TextChoices):
    AI_AUTO = "ai_auto", _("AI Auto")
    USER_PLATE = "user_plate", _("User Plate")
    USER_APP = "user_app", _("User App")


class FoodEntry(BaseModel):
    """
    Represents a specific food item identified in a meal log snapshot.
    Includes estimated nutritional values and metadata about its origin.
    """

    meal_log = models.ForeignKey(
        MealLog,
        on_delete=models.CASCADE,
        related_name="entries",
        help_text=_("The meal snapshot this food was detected in"),
    )
    label = models.CharField(
        max_length=100,
        help_text=_("Detected or user-labeled name of the food item"),
    )
    weight_g = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_("Portion size in grams"),
    )
    estimated_kcal = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_("Estimated calorie content"),
    )
    protein_g = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text=_("Estimated protein content in grams"),
    )
    carbs_g = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text=_("Estimated carbohydrate content in grams"),
    )
    fat_g = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text=_("Estimated fat content in grams"),
    )
    fibre_g = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text=_("Estimated dietary fiber in grams"),
    )
    source = models.CharField(
        max_length=20,
        choices=FoodEntrySource.choices,
        default=FoodEntrySource.AI_AUTO,
        help_text=_("Source of the label: AI, plate UI, or mobile app"),
    )
    manual_override = models.BooleanField(
        default=False,
        help_text=_("Whether the user manually corrected this label"),
    )

    def __str__(self):
        return f"{self.label} - {self.weight_g}g"

    class Meta:
        verbose_name = _("Food Entry")
        ordering = ["-created"]
