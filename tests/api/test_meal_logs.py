import io
from django.test import TestCase
from fastapi.testclient import TestClient
from app.api.main import app

client = TestClient(app)


def create_test_image():
    return io.BytesIO(b"fake image content")


class MealLogsTestCase(TestCase):
    def test_meal_logs_health(self):
        response = client.get("/api/v1/meal_logs/health")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["status"], "healthy")
        self.assertEqual(data["service"], "meal_logs")

    def test_create_meal_log_success(self):
    test_image = create_test_image()
    
    form_data = {
        "timestamp": "2024-01-15T12:30:00Z",
        "total_weight_g": 150.5,
        "delta_weight_g": 75.2,
        "device_id": "test_device_123",
    }
    
    files = {
        "image_file": ("test_image.jpg", test_image, "image/jpeg")
    }
    
    response = client.post("/api/v1/meal_logs/", data=form_data, files=files)
    
    assert response.status_code == 200
    data = response.json()
    
    assert "meal_log_id" in data
    assert "meal_session_id" in data
    assert data["status"] == "confirmed"
    assert "image_url" in data
    assert "ai_suggestions" in data
    assert "entries" in data
    assert "session_totals" in data
    
    assert len(data["ai_suggestions"]) > 0
    for suggestion in data["ai_suggestions"]:
        assert "label" in suggestion
        assert "confidence" in suggestion
        assert 0 <= suggestion["confidence"] <= 1
    
    assert len(data["entries"]) > 0
    for entry in data["entries"]:
        assert "id" in entry
        assert "label" in entry
        assert "weight_g" in entry
        assert "estimated_kcal" in entry
        assert "protein_g" in entry
        assert "carbs_g" in entry
        assert "fat_g" in entry
        assert "fibre_g" in entry
        assert "manual_override" in entry
        assert "source" in entry
    
    totals = data["session_totals"]
    assert "total_kcal" in totals
    assert "protein_g" in totals
    assert "carbs_g" in totals
    assert "fat_g" in totals
    assert "fibre_g" in totals


def test_create_meal_log_missing_fields():
    test_image = create_test_image()
    
    form_data = {
        "total_weight_g": 150.5,
        "delta_weight_g": 75.2,
        "device_id": "test_device_123",
    }
    
    files = {
        "image_file": ("test_image.jpg", test_image, "image/jpeg")
    }
    
    response = client.post("/api/v1/meal_logs/", data=form_data, files=files)
    
    assert response.status_code == 422
