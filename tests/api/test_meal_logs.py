import io
from django.test import TestCase
from fastapi.testclient import TestClient
from app.api.main import app

client = TestClient(app)


def create_test_image():
    return io.BytesIO(b"fake image content")


class MealLogsTestCase(TestCase):
    def test_meal_logs_health(self):
        response = client.get("/api/v1/meal_logs/health")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["status"], "healthy")
        self.assertEqual(data["service"], "meal_logs")

    def test_create_meal_log_success(self):
        test_image = create_test_image()

        form_data = {
            "timestamp": "2024-01-15T12:30:00Z",
            "total_weight_g": 150.5,
            "delta_weight_g": 75.2,
            "device_id": "test_device_123",
        }

        files = {
            "image_file": ("test_image.jpg", test_image, "image/jpeg")
        }

        response = client.post("/api/v1/meal_logs/", data=form_data, files=files)

        self.assertEqual(response.status_code, 200)
        data = response.json()

        self.assertIn("meal_log_id", data)
        self.assertIn("meal_session_id", data)
        self.assertEqual(data["status"], "confirmed")
        self.assertIn("image_url", data)
        self.assertIn("ai_suggestions", data)
        self.assertIn("entries", data)
        self.assertIn("session_totals", data)

        self.assertGreater(len(data["ai_suggestions"]), 0)
        for suggestion in data["ai_suggestions"]:
            self.assertIn("label", suggestion)
            self.assertIn("confidence", suggestion)
            self.assertGreaterEqual(suggestion["confidence"], 0)
            self.assertLessEqual(suggestion["confidence"], 1)

        self.assertGreater(len(data["entries"]), 0)
        for entry in data["entries"]:
            self.assertIn("id", entry)
            self.assertIn("label", entry)
            self.assertIn("weight_g", entry)
            self.assertIn("estimated_kcal", entry)
            self.assertIn("protein_g", entry)
            self.assertIn("carbs_g", entry)
            self.assertIn("fat_g", entry)
            self.assertIn("fibre_g", entry)
            self.assertIn("manual_override", entry)
            self.assertIn("source", entry)

        totals = data["session_totals"]
        self.assertIn("total_kcal", totals)
        self.assertIn("protein_g", totals)
        self.assertIn("carbs_g", totals)
        self.assertIn("fat_g", totals)
        self.assertIn("fibre_g", totals)


    def test_create_meal_log_missing_fields(self):
        test_image = create_test_image()

        form_data = {
            "total_weight_g": 150.5,
            "delta_weight_g": 75.2,
            "device_id": "test_device_123",
        }

        files = {
            "image_file": ("test_image.jpg", test_image, "image/jpeg")
        }

        response = client.post("/api/v1/meal_logs/", data=form_data, files=files)

        self.assertEqual(response.status_code, 422)
